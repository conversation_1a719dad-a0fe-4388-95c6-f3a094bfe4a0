import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Data interfaces
export interface PropertyCountItem {
  rpmName: string;
  stabilized: number;
  leaseUp: number;
  newConstruction: number;
  totalProperties: number;
  expectedPmFees: number;
}

export interface RegionalKpiItem {
  region: string;
  periodEndActuals: {
    rpms: number;
    vps: number;
    properties: number;
    propertiesPerRpm: number;
    workloadPerRpm: string; // "OPEN" or number
    rpmsPerVp: number;
  };
  forecastFullYear2025: {
    rpms: number;
    vps: number;
    properties: number;
    propertiesPerRpm: number;
    workloadPerRpm: string; // "OPEN" or number
    rpmsPerVp: number;
  };
}

export interface MarketKpiItem {
  market: string;
  periodEndActuals: {
    rpms: number;
    vps: number;
    properties: number;
    propertiesPerRpm: number;
    workloadPerRpm: string; // "OPEN" or number
    rpmsPerVp: number;
  };
  forecastFullYear2025: {
    rpms: number;
    vps: number;
    properties: number;
    propertiesPerRpm: number;
    workloadPerRpm: string; // "OPEN" or number
    rpmsPerVp: number;
  };
}

export interface StaffingKpiState {
  activeTab: 'propertyCount' | 'regionalKpis' | 'marketKpis';
  propertyCountData: PropertyCountItem[];
  regionalKpisData: RegionalKpiItem[];
  marketKpisData: MarketKpiItem[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: StaffingKpiState = {
  activeTab: 'propertyCount',
  propertyCountData: [
    {
      rpmName: 'John Smith',
      stabilized: 4,
      leaseUp: 0,
      newConstruction: 0,
      totalProperties: 4,
      expectedPmFees: 100000,
    },
    {
      rpmName: 'Margaret Woods',
      stabilized: 3,
      leaseUp: 0,
      newConstruction: 0,
      totalProperties: 3,
      expectedPmFees: 200000,
    },
    {
      rpmName: 'Kelly Diamond',
      stabilized: 2,
      leaseUp: 2,
      newConstruction: 0,
      totalProperties: 4,
      expectedPmFees: 75000,
    },
    {
      rpmName: 'Trisha O\'Neil',
      stabilized: 1,
      leaseUp: 1,
      newConstruction: 0,
      totalProperties: 2,
      expectedPmFees: 201000,
    },
    {
      rpmName: 'Sam O\'Hara',
      stabilized: 1,
      leaseUp: 1,
      newConstruction: 0,
      totalProperties: 2,
      expectedPmFees: 75000,
    },
  ],
  regionalKpisData: [
    {
      region: 'Central',
      periodEndActuals: {
        rpms: 100,
        vps: 2,
        properties: 404,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 50,
      },
      forecastFullYear2025: {
        rpms: 28269,
        vps: 0,
        properties: 404,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 60,
      },
    },
    {
      region: 'East',
      periodEndActuals: {
        rpms: 100,
        vps: 1,
        properties: 404,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 100,
      },
      forecastFullYear2025: {
        rpms: 28269,
        vps: 0,
        properties: 404,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 60,
      },
    },
    {
      region: 'West',
      periodEndActuals: {
        rpms: 100,
        vps: 2,
        properties: 404,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 50,
      },
      forecastFullYear2025: {
        rpms: 28269,
        vps: 0,
        properties: 404,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 60,
      },
    },
    {
      region: 'Consol.',
      periodEndActuals: {
        rpms: 300,
        vps: 5,
        properties: 1212,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 60,
      },
      forecastFullYear2025: {
        rpms: 84807,
        vps: 0,
        properties: 404,
        propertiesPerRpm: 4,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 60,
      },
    },
  ],
  marketKpisData: [
    {
      market: 'South FL',
      periodEndActuals: {
        rpms: 4,
        vps: 1,
        properties: 25,
        propertiesPerRpm: 6.3,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 4.0,
      },
      forecastFullYear2025: {
        rpms: 4,
        vps: 1,
        properties: 25,
        propertiesPerRpm: 6.3,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 4.0,
      },
    },
    {
      market: 'North FL',
      periodEndActuals: {
        rpms: 4,
        vps: 1,
        properties: 25,
        propertiesPerRpm: 6.3,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 4.0,
      },
      forecastFullYear2025: {
        rpms: 4,
        vps: 1,
        properties: 25,
        propertiesPerRpm: 6.3,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 4.0,
      },
    },
    {
      market: 'West FL',
      periodEndActuals: {
        rpms: 2,
        vps: 1,
        properties: 24,
        propertiesPerRpm: 12.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 2.0,
      },
      forecastFullYear2025: {
        rpms: 2,
        vps: 1,
        properties: 24,
        propertiesPerRpm: 12.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 2.0,
      },
    },
    {
      market: 'Central FL',
      periodEndActuals: {
        rpms: 2,
        vps: 1,
        properties: 24,
        propertiesPerRpm: 12.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 2.0,
      },
      forecastFullYear2025: {
        rpms: 2,
        vps: 1,
        properties: 24,
        propertiesPerRpm: 12.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 2.0,
      },
    },
    {
      market: 'GA/Gulf',
      periodEndActuals: {
        rpms: 1,
        vps: 0,
        properties: 25,
        propertiesPerRpm: 25.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
      forecastFullYear2025: {
        rpms: 1,
        vps: 0,
        properties: 25,
        propertiesPerRpm: 25.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
    },
    {
      market: 'Carolinas',
      periodEndActuals: {
        rpms: 1,
        vps: 0,
        properties: 19,
        propertiesPerRpm: 19.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
      forecastFullYear2025: {
        rpms: 1,
        vps: 0,
        properties: 19,
        propertiesPerRpm: 19.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
    },
    {
      market: 'TN',
      periodEndActuals: {
        rpms: 1,
        vps: 0,
        properties: 16,
        propertiesPerRpm: 16.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
      forecastFullYear2025: {
        rpms: 1,
        vps: 0,
        properties: 16,
        propertiesPerRpm: 16.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
    },
    {
      market: 'DC Metro',
      periodEndActuals: {
        rpms: 1,
        vps: 0,
        properties: 17,
        propertiesPerRpm: 17.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
      forecastFullYear2025: {
        rpms: 1,
        vps: 0,
        properties: 17,
        propertiesPerRpm: 17.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
    },
    {
      market: 'PA/NJ',
      periodEndActuals: {
        rpms: 6,
        vps: 0,
        properties: 20,
        propertiesPerRpm: 3.3,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
      forecastFullYear2025: {
        rpms: 6,
        vps: 0,
        properties: 20,
        propertiesPerRpm: 3.3,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 0,
      },
    },
    {
      market: 'NY/MA/CT',
      periodEndActuals: {
        rpms: 2,
        vps: 1,
        properties: 2,
        propertiesPerRpm: 1.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 2.0,
      },
      forecastFullYear2025: {
        rpms: 2,
        vps: 1,
        properties: 2,
        propertiesPerRpm: 1.0,
        workloadPerRpm: 'OPEN',
        rpmsPerVp: 2.0,
      },
    },
  ],
  loading: false,
  error: null,
};

// Create slice
const staffingKpiSlice = createSlice({
  name: 'staffingKpi',
  initialState,
  reducers: {
    setActiveTab: (state, action: PayloadAction<'propertyCount' | 'regionalKpis' | 'marketKpis'>) => {
      state.activeTab = action.payload;
    },
    setPropertyCountData: (state, action: PayloadAction<PropertyCountItem[]>) => {
      state.propertyCountData = action.payload;
    },
    setRegionalKpisData: (state, action: PayloadAction<RegionalKpiItem[]>) => {
      state.regionalKpisData = action.payload;
    },
    setMarketKpisData: (state, action: PayloadAction<MarketKpiItem[]>) => {
      state.marketKpisData = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

// Export actions
export const {
  setActiveTab,
  setPropertyCountData,
  setRegionalKpisData,
  setMarketKpisData,
  setLoading,
  setError,
} = staffingKpiSlice.actions;

// Export reducer
export default staffingKpiSlice.reducer;
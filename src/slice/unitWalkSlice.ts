import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Data interfaces
export interface UnitWalkItem {
  region: string;
  beginning: number;
  additions: number;
  losses: number;
  current: number;
}

export interface YTDUnitWalkItem {
  market: string;
  beginning: number;
  additions: number;
  losses: number;
  current: number;
}

export interface UnitWalkFilters {
  year: string;
  month: string[];
  region: string[];
  property: string[];
}

export interface FilterOption {
  value: string;
  label: string;
}

export interface UnitWalkState {
  activeTab: 'unitWalk' | 'ytdUnitWalk';
  filters: UnitWalkFilters;
  unitWalkData: UnitWalkItem[];
  ytdUnitWalkData: YTDUnitWalkItem[];
  availableRegions: FilterOption[];
  availableProperties: FilterOption[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: UnitWalkState = {
  activeTab: 'unitWalk',
  filters: {
    year: '2025',
    month: [],
    region: [],
    property: [],
  },
  unitWalkData: [
    { region: 'South FL', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'North FL', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'West FL', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'Central FL', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'GA/Gulf', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'Carolinas', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'TN', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'DC Metro', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'PA/NJ', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
    { region: 'NY/MA/CT', beginning: 58177, additions: 17299, losses: -10739, current: 64737 },
  ],
  ytdUnitWalkData: [
    { market: 'South Florida', beginning: 101913, additions: 21917, losses: -18117, current: 105713 },
  ],
  availableRegions: [
    { value: 'south-fl', label: 'South FL' },
    { value: 'north-fl', label: 'North FL' },
    { value: 'west-fl', label: 'West FL' },
    { value: 'central-fl', label: 'Central FL' },
    { value: 'ga-gulf', label: 'GA/Gulf' },
    { value: 'carolinas', label: 'Carolinas' },
    { value: 'tn', label: 'TN' },
    { value: 'dc-metro', label: 'DC Metro' },
    { value: 'pa-nj', label: 'PA/NJ' },
    { value: 'ny-ma-ct', label: 'NY/MA/CT' },
  ],
  availableProperties: [],
  loading: false,
  error: null,
};

// Create slice
const unitWalkSlice = createSlice({
  name: 'unitWalk',
  initialState,
  reducers: {
    setActiveTab: (state, action: PayloadAction<'unitWalk' | 'ytdUnitWalk'>) => {
      state.activeTab = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<UnitWalkFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setUnitWalkData: (state, action: PayloadAction<UnitWalkItem[]>) => {
      state.unitWalkData = action.payload;
    },
    setYTDUnitWalkData: (state, action: PayloadAction<YTDUnitWalkItem[]>) => {
      state.ytdUnitWalkData = action.payload;
    },
    setAvailableRegions: (state, action: PayloadAction<FilterOption[]>) => {
      state.availableRegions = action.payload;
    },
    setAvailableProperties: (state, action: PayloadAction<FilterOption[]>) => {
      state.availableProperties = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
  },
});

// Export actions
export const {
  setActiveTab,
  setFilters,
  setUnitWalkData,
  setYTDUnitWalkData,
  setAvailableRegions,
  setAvailableProperties,
  setLoading,
  setError,
  resetFilters,
} = unitWalkSlice.actions;

// Export reducer
export default unitWalkSlice.reducer;
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface BusinessDevelopmentItem {
  id: string;
  type: 'Win' | 'Loss';
  client: string;
  units: number;
  status: string;
  manager: string;
  pmFeePercent: number;
  pmFeePer25: number;
  annualPmFees: number;
}

export interface BusinessDevelopmentState {
  items: BusinessDevelopmentItem[];
  loading: boolean;
  error: string | null;
  lastUpdated: string;
}

const mockData: BusinessDevelopmentItem[] = [
  {
    id: '1',
    type: 'Win',
    client: 'UBS',
    units: 1000,
    status: 'New Construction',
    manager: 'RPM',
    pmFeePercent: 2.0,
    pmFeePer25: 25000,
    annualPmFees: 200000,
  },
  {
    id: '2',
    type: 'Win',
    client: 'UBS',
    units: 1000,
    status: 'Stabilized',
    manager: 'Asset Living',
    pmFeePercent: 2.0,
    pmFeePer25: 25000,
    annualPmFees: 200000,
  },
  {
    id: '3',
    type: 'Win',
    client: 'UBS',
    units: 1000,
    status: 'Stabilized',
    manager: 'Greystar',
    pmFeePercent: 2.0,
    pmFeePer25: 25000,
    annualPmFees: 200000,
  },
  {
    id: '4',
    type: 'Loss',
    client: '<PERSON>',
    units: -1000,
    status: 'Stabilized',
    manager: 'Greystar',
    pmFeePercent: 2.0,
    pmFeePer25: 25000,
    annualPmFees: -200000,
  },
];

const initialState: BusinessDevelopmentState = {
  items: mockData,
  loading: false,
  error: null,
  lastUpdated: 'January 2025',
};

export const businessDevelopmentSlice = createSlice({
  name: 'businessDevelopment',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setBusinessDevelopmentData: (
      state,
      action: PayloadAction<{
        items: BusinessDevelopmentItem[];
        lastUpdated: string;
      }>,
    ) => {
      state.items = action.payload.items;
      state.lastUpdated = action.payload.lastUpdated;
    },
    setLastUpdated: (state, action: PayloadAction<string>) => {
      state.lastUpdated = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setBusinessDevelopmentData,
  setLastUpdated,
} = businessDevelopmentSlice.actions;

export default businessDevelopmentSlice;
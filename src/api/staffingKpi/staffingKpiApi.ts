import axios from 'axios';
import {
  StaffingRPMKPITypes,
  StaffingKPIPayload,
  StaffingMarketKPITypes,
  StaffingRegionalKPITypes,
} from './staffingKpiApi.types';
import { DEFAULT_REPORTS_API_PAYLOAD } from '@/constants/constants';

const URL = import.meta.env.VITE_API_URL;

const defaultPayload =DEFAULT_REPORTS_API_PAYLOAD

export const staffingKpiRPMApi = async (
  payload: StaffingKPIPayload,
): Promise<{ data: StaffingRPMKPITypes[] }> => {
  if (!payload) {
    payload = defaultPayload;
  }
  const responce = await axios.post(`${URL}/staffing-kpi-rpm`, payload);
  const { data } = responce.data;
  return { data };
};

export const staffingKpiRegionalApi = async (
  payload: StaffingKPIPayload,
): Promise<{ data: StaffingRegionalKPITypes[] }> => {
  if (!payload) {
    payload = defaultPayload;
  }
  const responce = await axios.post(`${URL}/staffing-kpi-regional`, payload);
  const { data } = responce.data;
  return { data };
};

export const staffingKpiMarketApi = async (
  payload: StaffingKPIPayload,
): Promise<{ data: StaffingMarketKPITypes[] }> => {
  if (!payload) {
    payload = defaultPayload;
  }
  const responce = await axios.post(`${URL}/staffing-kpi-market`, payload);
  const { data } = responce.data;
  return { data };
};

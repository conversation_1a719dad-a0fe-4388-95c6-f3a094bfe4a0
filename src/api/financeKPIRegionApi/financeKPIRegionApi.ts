import axios from 'axios';
import {
  FinanceKPIPayload,
  FinanceKPIRegionActualResponse,
  FinanceKPIRegionForecastResponse,
  FinanceKPIRegionWiseResponce,
} from './financeKPIRegionApi.types';

const URL = import.meta.env.VITE_API_URL;
export const financeKpiRegionWiseApi = async (
  payload: FinanceKPIPayload,
): Promise<{ data: FinanceKPIRegionWiseResponce[] }> => {
  const response = await axios.post(`${URL}/finance-kpi-regionwise`, payload);
  const { data } = response.data;
  return { data };
};

export const financeKpiRegionForecastApi = async (
  payload: FinanceKPIPayload,
): Promise<{ data: FinanceKPIRegionForecastResponse[] }> => {
  const response = await axios.post(
    `${URL}/finance-kpi-market-region-forecast`,
    payload,
  );
  const { data } = response.data;
  return { data };
};

export const financeKpiRegionActualApi = async (
  payload: FinanceKPIPayload,
): Promise<{ data: FinanceKPIRegionActualResponse[] }> => {
  const response = await axios.post(
    `${URL}/finance-kpi-market-region-actual`,
    payload,
  );
  const { data } = response.data;

  return { data };
};

export const financeKpiYTDActualVsBudgetApi = async (
  payload: FinanceKPIPayload,
): Promise<{ data: FinanceKPIRegionWiseResponce[] }> => {
  const response = await axios.post(
    `${URL}/finance-kpi-ytd-actual-vs-budget`,
    payload,
  );
  const { data } = response.data;

  return { data };
};

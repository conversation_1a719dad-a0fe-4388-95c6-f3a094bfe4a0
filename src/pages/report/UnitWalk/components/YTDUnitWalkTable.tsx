import React from 'react';
import { RootState } from '@/store';
import { FileUp } from 'lucide-react';
import { useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';

const YTDUnitWalkTable: React.FC = () => {
  const { ytdUnitWalkData } = useSelector((state: RootState) => state.unitWalk);

  const formatNumber = (value: number) => {
    return value.toLocaleString();
  };

  const formatLoss = (value: number) => {
    return `(${Math.abs(value).toLocaleString()})`;
  };

  const handleExport = () => {
    // Export functionality to be implemented
    console.log('Export YTD Unit Walk data');
  };

  return (
    <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg p-4">
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">YTD Unit Walk</h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={handleExport}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <div className="border border-[#F3F4FF] overflow-hidden">
        <table className="min-w-full border-collapse">
          <thead>
            <tr className="bg-[#43298F] text-white">
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                Market
              </th>
              <th
                className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-right"
                style={{ borderLeft: '2px solid black' }}
              >
                Beginning
              </th>
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-right">
                Additions
              </th>
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-right">
                Losses
              </th>
              <th
                className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-right"
                style={{ borderRight: '2px solid black' }}
              >
                Current
              </th>
            </tr>
          </thead>
          <tbody className="bg-white">
            {ytdUnitWalkData.map((item, index) => (
              <tr
                key={index}
                className={`border-b border-[#DEEFFF] ${
                  index % 2 === 1 ? 'bg-[#F4F4FF]' : 'bg-white'
                }`}
              >
                <td className="px-2 py-0 whitespace-nowrap text-left font-medium border-r border-[#DEEFFF]">
                  {item.market}
                </td>
                <td
                  className="px-2 py-0 whitespace-nowrap text-right border-r border-[#DEEFFF]"
                  style={{ borderLeft: '2px solid #DEEFFF' }}
                >
                  {formatNumber(item.beginning)}
                </td>
                <td className="px-2 py-0 whitespace-nowrap text-right border-r border-[#DEEFFF]">
                  {formatNumber(item.additions)}
                </td>
                <td className="px-2 py-0 whitespace-nowrap text-right text-red-600 border-r border-[#DEEFFF]">
                  {formatLoss(item.losses)}
                </td>
                <td
                  className="px-2 py-0 whitespace-nowrap text-right"
                  style={{ borderRight: '2px solid #DEEFFF' }}
                >
                  {formatNumber(item.current)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default YTDUnitWalkTable;

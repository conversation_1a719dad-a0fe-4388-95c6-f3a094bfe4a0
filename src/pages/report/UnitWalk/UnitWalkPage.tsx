import React from 'react';
import { setActiveTab } from '@/slice/unitWalkSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Container } from '@/components/common/container';
import ReportFilters from '../IncomeReport/components/ReportFilters';
import UnitWalkTable from './components/UnitWalkTable';
import YTDUnitWalkTable from './components/YTDUnitWalkTable';

const UnitWalkPage: React.FC = () => {
  const dispatch = useDispatch();
  const { activeTab, loading, error } = useSelector(
    (state: RootState) => state.unitWalk,
  );

  const handleTabChange = (value: string) => {
    dispatch(setActiveTab(value as 'unitWalk' | 'ytdUnitWalk'));
  };

  const TabsContentUi = ({
    loading,
    error,
    component,
  }: {
    loading?: boolean;
    error?: string | null;
    component: React.ReactNode;
  }) => {
    return (
      <div>
        {loading ? (
          <div className="h-[60vh] flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#43298F]"></div>
          </div>
        ) : error ? (
          <div className="text-red-500 text-center py-8">{error}</div>
        ) : (
          component
        )}
      </div>
    );
  };

  return (
    <Container title="Unit Walk" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>

        <div className="min-w-[1024px]">
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid w-full grid-cols-2 mb-6 bg-white border border-[#43298F]">
              <TabsTrigger
                value="unitWalk"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
              >
                Unit Walk
              </TabsTrigger>
              <TabsTrigger
                value="ytdUnitWalk"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
              >
                YTD Unit Walk
              </TabsTrigger>
            </TabsList>

            <TabsContent value="unitWalk">
              <TabsContentUi
                loading={loading}
                error={error}
                component={<UnitWalkTable />}
              />
            </TabsContent>

            <TabsContent value="ytdUnitWalk">
              <TabsContentUi
                loading={loading}
                error={error}
                component={<YTDUnitWalkTable />}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Container>
  );
};

export default UnitWalkPage;

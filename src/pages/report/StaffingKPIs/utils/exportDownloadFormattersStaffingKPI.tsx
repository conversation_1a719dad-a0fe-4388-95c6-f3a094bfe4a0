import {
  StaffingMarketKPITypes,
  StaffingRegionalKPITypes,
  StaffingRPMKPITypes,
} from '@/api/staffingKpi/staffingKpiApi.types';
import ExcelJS from 'exceljs';
import { tableRowOrderFormateStaffingRegional } from './helperStaffingKPI';

const purpleColor = { argb: 'FF43298F' };
const lightBlueColor = { argb: 'FFDEEFFF' };
const lightPurpleColor = { argb: 'FFF4F4FF' };
const whiteColor = { argb: 'FFFFFFFF' };

const headingFill = {
  type: 'pattern' as const,
  pattern: 'solid' as const,
  fgColor: purpleColor,
};

const rowLightPurpleColor = {
  type: 'pattern' as const,
  pattern: 'solid' as const,
  fgColor: lightPurpleColor,
};

const mainHeadingFont = { bold: true, color: whiteColor, size: 12 };
const subHeadingFont = { bold: true, color: whiteColor };
const alignmentCenter = {
  horizontal: 'center' as const,
  vertical: 'middle' as const,
  wrapText: true,
};
// const alignmentRight = {
//   horizontal: 'right' as const,
//   vertical: 'middle' as const,
// };
// const alignmentLeft = {
//   horizontal: 'left' as const,
//   vertical: 'middle' as const,
// };

const gropHeaderBroder = {
  style: 'thin' as ExcelJS.BorderStyle,
  color: { argb: 'FF000000' },
};

const groupFullBorder = {
  top: gropHeaderBroder,
  left: gropHeaderBroder,
  bottom: gropHeaderBroder,
  right: gropHeaderBroder,
};

export const downloadExcelStaffingPropertyCount = async (
  rpmData: StaffingRPMKPITypes[],
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Property Count ');
  console.log('rpmData', rpmData);

  worksheet.mergeCells('A1:F1');
  const title = worksheet.getCell('A1');
  title.value = "Property Count and '25 Expected Annual PM Fees per RPM";
  title.font = { size: 14, color: purpleColor, bold: true };

  worksheet.addRow('');
  worksheet.addRow('');
  const subHeadingsRow = [
    'RPM Name ',
    'Stabilized',
    'Lease-Up',
    'New Construction',
    'Total Properties',
    'Expected PM Fees',
  ];
  // worksheet.columns = columsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = 38;

  worksheet.columns = [
    { width: 15 },
    { width: 13 },
    { width: 12 },
    { width: 16 },
    { width: 16 },
    { width: 16 },
  ];

  subheadings.eachCell((cell) => {
    cell.font = subHeadingFont;
    cell.fill = headingFill;
    cell.alignment = alignmentCenter;
    cell.border = groupFullBorder;
  });

  rpmData.forEach(
    (
      {
        expected_pm_fees,
        lease_up,
        new_construction,
        rpm_name,
        stabilized,
        total_properties,
      },
      index,
    ) => {
      const rowCells = worksheet.addRow([
        rpm_name,
        stabilized,
        lease_up,
        new_construction,
        total_properties,
        expected_pm_fees,
      ]);

      if (index % 2 === 0) {
        rowCells.eachCell((cell) => {
          cell.fill = rowLightPurpleColor;
        });
      }
    },
  );

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Staffing-Property-Count`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

export const downloadExcelStaffingRegionalKPI = async (
  rpmData: StaffingRegionalKPITypes[],
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Regional Average');
  console.log('rpmData', rpmData);

  worksheet.mergeCells('A1:J1');
  const title = worksheet.getCell('A1');
  title.value = "Regional Average Staffing KPI's";
  title.font = { size: 14, color: purpleColor, bold: true };

  // creating group headings
  worksheet.addRow('');
  worksheet.addRow('');
  const groupHeadings = worksheet.addRow([
    '',
    'Period-End Actuals',
    '',
    '',
    '',
    'Forecast Full Year 2025',
    '',
    '',
    '',
  ]);
  groupHeadings.eachCell((cell, colNum) => {
    if (colNum !== 1) {
      cell.font = mainHeadingFont;
      cell.fill = headingFill;
      cell.alignment = alignmentCenter;
      cell.border = groupFullBorder;
    }
  });
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:E${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `F${worksheet.lastRow?.number}:I${worksheet.lastRow?.number}`,
  );

  const subHeadingsRow = [
    '',

    'Central',
    'East',
    'West',
    'Consol.',

    'Central',
    'East',
    'West',
    'Consol.',
  ];
  // worksheet.columns = columsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = 38;

  worksheet.columns = [
    { width: 15 },
    { width: 13 },
    { width: 12 },
    { width: 16 },
    { width: 16 },
    { width: 16 },
  ];

  subheadings.eachCell((cell) => {
    cell.font = subHeadingFont;
    cell.fill = headingFill;
    cell.alignment = alignmentCenter;
    cell.border = groupFullBorder;
  });

  tableRowOrderFormateStaffingRegional.forEach(({ label, key }, index) => {
    const item = rpmData?.find((item) => item?.parameter_name === key);
    const rowCells = worksheet.addRow([
      label,
      item?.central_actual,
      item?.east_actual,
      item?.west_actual,
      item?.consolidated_actual,
      //     forecast
      item?.central_forecast,
      item?.east_forecast,
      item?.west_forecast,
      item?.consolidated_forecast,
    ]);

    if (index % 2 === 0) {
      rowCells.eachCell((cell) => {
        cell.fill = rowLightPurpleColor;
      });
    }
  });

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Staffing-Regional Average`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

export const downloadExcelStaffingMarketKPI = async (
  marketData: StaffingMarketKPITypes[],
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Market Level');
  console.log('marketData', marketData);

  worksheet.mergeCells('A1:J1');
  const title = worksheet.getCell('A1');
  title.value = "Market-Level Staffing KPI's";
  title.font = { size: 14, color: purpleColor, bold: true };

  // creating group headings
  worksheet.addRow('');
  worksheet.addRow('');
  const groupHeadings = worksheet.addRow([
    '',

    'Period-End Actuals',
    '',
    '',
    '',
    '',
    '',
    //
    'Forecast Full Year 2025',
    '',
    '',
    '',
    '',
    '',
  ]);
  groupHeadings.eachCell((cell, colNum) => {
    if (colNum !== 1) {
      cell.font = mainHeadingFont;
      cell.fill = headingFill;
      cell.alignment = alignmentCenter;
      cell.border = groupFullBorder;
    }
  });
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:G${worksheet.lastRow?.number}`,
  );
  worksheet.mergeCells(
    `H${worksheet.lastRow?.number}:M${worksheet.lastRow?.number}`,
  );

  const subHeadingsRow = [
    '',
    // Actual
    "RPM's",
    "VP's",
    'Properties',
    'Properties/RPM.',
    'Workload/RPM',
    "RPM's/VP",
    // forecast
    "RPM's",
    "VP's",
    'Properties',
    'Properties/RPM.',
    'Workload/RPM',
    "RPM's/VP",
  ];
  // worksheet.columns = columsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = 38;

  worksheet.columns = [
    { width: 13 },

    { width: 10 },
    { width: 10 },
    { width: 14 },
    { width: 16 },
    { width: 16 },
    { width: 10 },

    { width: 10 },
    { width: 10 },
    { width: 14 },
    { width: 16 },
    { width: 16 },
    { width: 10 },
  ];

  subheadings.eachCell((cell) => {
    cell.font = subHeadingFont;
    cell.fill = headingFill;
    cell.alignment = alignmentCenter;
    cell.border = groupFullBorder;
  });

  const totals = {
    rpm_actual: marketData?.reduce(
      (sum, item) => sum + (item?.rpm_actual ?? 0),
      0,
    ),
    vp_actual: marketData?.reduce(
      (sum, item) => sum + (item?.vp_actual ?? 0),
      0,
    ),
    properties_actual: marketData?.reduce(
      (sum, item) => sum + (item?.properties_actual ?? 0),
      0,
    ),
    properties_rpm_actual: marketData?.reduce(
      (sum, item) => sum + (item?.properties_rpm_actual ?? 0),
      0,
    ),
    workload_rpm_actual: marketData?.reduce(
      (sum, item) => sum + (item?.workload_rpm_actual ?? 0),
      0,
    ),
    rpm_vp_actual: marketData?.reduce(
      (sum, item) => sum + (item?.rpm_vp_actual ?? 0),
      0,
    ),
    rpm_forecast: marketData?.reduce(
      (sum, item) => sum + (item?.rpm_forecast ?? 0),
      0,
    ),
    vp_forecast: marketData?.reduce(
      (sum, item) => sum + (item?.vp_forecast ?? 0),
      0,
    ),
    properties_forecast: marketData?.reduce(
      (sum, item) => sum + (item?.properties_forecast ?? 0),
      0,
    ),
    properties_rpm_forecast: marketData?.reduce(
      (sum, item) => sum + (item?.properties_rpm_forecast ?? 0),
      0,
    ),
    workload_rpm_forecast: marketData?.reduce(
      (sum, item) => sum + (item?.workload_rpm_forecast ?? 0),
      0,
    ),
    rpm_vp_forecast: marketData?.reduce(
      (sum, item) => sum + (item?.rpm_vp_forecast ?? 0),
      0,
    ),
  };

  marketData?.forEach((item, index) => {
    const rowCells = worksheet.addRow([
      item.market,
      item?.rpm_actual,
      item?.vp_actual,
      item?.properties_actual,
      item?.properties_rpm_actual,
      item?.workload_rpm_actual,
      item?.rpm_vp_actual,
      //     forecast
      item?.rpm_forecast,
      item?.vp_forecast,
      item?.properties_forecast,
      item?.properties_rpm_forecast,
      item?.workload_rpm_forecast,
      item?.rpm_vp_forecast,
    ]);

    if (index % 2 === 0) {
      rowCells.eachCell((cell) => {
        cell.fill = rowLightPurpleColor;
      });
    }
  });

  const totalRow = worksheet.addRow([
    'Total',
    totals.rpm_actual,
    totals.vp_actual,
    totals.properties_actual,
    totals.properties_rpm_actual,
    totals.workload_rpm_actual,
    totals.rpm_vp_actual,
    totals.rpm_forecast,
    totals.vp_forecast,
    totals.properties_forecast,
    totals.properties_rpm_forecast,
    totals.workload_rpm_forecast,
    totals.rpm_vp_forecast,
  ]);

  totalRow.eachCell((cell) => {
    cell.font = { bold: true };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: lightBlueColor,
    };
  });

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Staffing-Regional Average`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

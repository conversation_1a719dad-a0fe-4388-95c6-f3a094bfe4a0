import { StaffingMarketKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableBodyRowTotal,
  CommonTableBodyTotalCell,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingMarketKPI } from '../utils/exportDownloadFormattersStaffingKPI';

interface PropsTypesMarketTable {
  marketTableData: StaffingMarketKPITypes[];
  datePeriod: string;
}

export default function MarketLevelStaffingTable(props: PropsTypesMarketTable) {
  const { marketTableData, datePeriod } = props;

  const marketTotals = {
    rpm_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.rpm_actual ?? 0),
      0,
    ),
    vp_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.vp_actual ?? 0),
      0,
    ),
    properties_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.properties_actual ?? 0),
      0,
    ),
    properties_rpm_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.properties_rpm_actual ?? 0),
      0,
    ),
    workload_rpm_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.workload_rpm_actual ?? 0),
      0,
    ),
    rpm_vp_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.rpm_vp_actual ?? 0),
      0,
    ),
    rpm_forecast: marketTableData?.reduce(
      (sum, item) => sum + (item?.rpm_forecast ?? 0),
      0,
    ),
    vp_forecast: marketTableData?.reduce(
      (sum, item) => sum + (item?.vp_forecast ?? 0),
      0,
    ),
    properties_forecast: marketTableData?.reduce(
      (sum, item) => sum + (item?.properties_forecast ?? 0),
      0,
    ),
    properties_rpm_forecast: marketTableData?.reduce(
      (sum, item) => sum + (item?.properties_rpm_forecast ?? 0),
      0,
    ),
    workload_rpm_forecast: marketTableData?.reduce(
      (sum, item) => sum + (item?.workload_rpm_forecast ?? 0),
      0,
    ),
    rpm_vp_forecast: marketTableData?.reduce(
      (sum, item) => sum + (item?.rpm_vp_forecast ?? 0),
      0,
    ),
  };

  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() => {
            downloadExcelStaffingMarketKPI(marketTableData);
          }}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <CommonTable>
        <thead>
          <CommonTableMainHeaderRow>
            <th></th>
            <CommonTableHeadingMergeCell colSpan={6}>
              Period-End Actuals
            </CommonTableHeadingMergeCell>
            <CommonTableHeadingMergeCell colSpan={6}>
              Forecast Full Year 2025
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>

          <CommonTableSubHeaderRow>
            <th className="text-left">Market</th>
            <CommonTableHeadingCell borderLeft>RPM's</CommonTableHeadingCell>
            <CommonTableHeadingCell>VP's</CommonTableHeadingCell>
            <CommonTableHeadingCell>Properties</CommonTableHeadingCell>
            <CommonTableHeadingCell>Properties/RPM</CommonTableHeadingCell>
            <CommonTableHeadingCell>Workload/RPM</CommonTableHeadingCell>
            <CommonTableHeadingCell>RPM's/VP</CommonTableHeadingCell>

            <CommonTableHeadingCell>RPM's</CommonTableHeadingCell>
            <CommonTableHeadingCell>VP's</CommonTableHeadingCell>
            <CommonTableHeadingCell>Properties</CommonTableHeadingCell>
            <CommonTableHeadingCell>Properties/RPM</CommonTableHeadingCell>
            <CommonTableHeadingCell>Workload/RPM</CommonTableHeadingCell>
            <CommonTableHeadingCell>RPM's/VP</CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>

        <tbody>
          {marketTableData?.map((item) => {
            return (
              <CommonTableBodyRow key={item?.market}>
                <td className="text-start px-1">{item?.market}</td>
                {/* actuals */}
                <CommonTableBodyCell borderLeft={true}>
                  {item?.rpm_actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell>{item?.vp_actual}</CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.properties_actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.properties_rpm_actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.workload_rpm_actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight={true}>
                  {item?.rpm_vp_actual}
                </CommonTableBodyCell>

                {/* forecast */}
                <CommonTableBodyCell>{item?.rpm_forecast}</CommonTableBodyCell>
                <CommonTableBodyCell>{item?.vp_forecast}</CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.properties_forecast}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.properties_rpm_forecast}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.workload_rpm_forecast}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight={true}>
                  {item?.rpm_vp_forecast}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
          <CommonTableBodyRowTotal>
            {/* <td className="text-start px-1 font-bold">Total</td> */}
            <CommonTableBodyTotalCell className="text-start ">
              TOTAL
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell borderLeft={true}>
              {marketTotals.rpm_actual}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.vp_actual}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.properties_actual}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.properties_rpm_actual}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.workload_rpm_actual}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell borderRight={true}>
              {marketTotals.rpm_vp_actual}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.rpm_forecast}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.vp_forecast}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {marketTotals.properties_forecast}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {marketTotals.properties_rpm_forecast}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell>
              {marketTotals.workload_rpm_forecast}
            </CommonTableBodyTotalCell>
            <CommonTableBodyTotalCell borderRight={true}>
              {marketTotals.rpm_vp_forecast}
            </CommonTableBodyTotalCell>
          </CommonTableBodyRowTotal>
        </tbody>
      </CommonTable>
    </div>
  );
}

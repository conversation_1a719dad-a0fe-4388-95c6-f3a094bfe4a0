import { StaffingRegionalKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingRegionalKPI } from '../utils/exportDownloadFormattersStaffingKPI';
import { tableRowOrderFormateStaffingRegional } from '../utils/helperStaffingKPI';

interface PropsTypesRegionalTable {
  regionalTableData: StaffingRegionalKPITypes[];
  datePeriod: string;
}

export default function RegionalAverageTable(props: PropsTypesRegionalTable) {
  const { regionalTableData, datePeriod } = props;
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() => {
            downloadExcelStaffingRegionalKPI(regionalTableData);
          }}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>
      <CommonTable>
        <thead>
          <CommonTableMainHeaderRow height="h-6">
            <th className="w-[120px]"></th>
            <CommonTableHeadingMergeCell colSpan={8}>
              Regional Average Staffing KPI's
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>

          <CommonTableMainHeaderRow>
            <th></th>

            <CommonTableHeadingMergeCell colSpan={4}>
              Period-End Actuals
            </CommonTableHeadingMergeCell>
            <CommonTableHeadingMergeCell colSpan={4}>
              Forecast Full Year 2025
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>

          <CommonTableSubHeaderRow>
            <th></th>
            <CommonTableHeadingCell borderLeft>Central</CommonTableHeadingCell>
            <CommonTableHeadingCell>East</CommonTableHeadingCell>
            <CommonTableHeadingCell>West</CommonTableHeadingCell>
            <CommonTableHeadingCell>Consol.</CommonTableHeadingCell>

            <CommonTableHeadingCell>Central</CommonTableHeadingCell>
            <CommonTableHeadingCell>East</CommonTableHeadingCell>
            <CommonTableHeadingCell>West</CommonTableHeadingCell>
            <CommonTableHeadingCell>Consol.</CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>

        <tbody>
          {tableRowOrderFormateStaffingRegional?.map(({ label, key }) => {
            const item = regionalTableData?.find(
              (item) => item?.parameter_name === key,
            );
            return (
              <CommonTableBodyRow key={label}>
                <td>
                  <div className="text-left px-1">{label}</div>
                </td>
                {/* actuals */}
                <CommonTableBodyCell borderLeft>
                  {item?.central_actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell>{item?.east_actual}</CommonTableBodyCell>
                <CommonTableBodyCell>{item?.west_actual}</CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {item?.consolidated_actual}
                </CommonTableBodyCell>

                {/* Forecast */}
                <CommonTableBodyCell>
                  {item?.central_forecast}
                </CommonTableBodyCell>
                <CommonTableBodyCell>{item?.east_forecast}</CommonTableBodyCell>
                <CommonTableBodyCell>{item?.west_forecast}</CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {item?.consolidated_forecast}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
        </tbody>
      </CommonTable>
    </div>
  );
}

import { StaffingRPMKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableHeadingCell,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingPropertyCount } from '../utils/exportDownloadFormattersStaffingKPI';

interface PropsTypes {
  rpmData: StaffingRPMKPITypes[];
  datePeriod:string
}

export default function PropertyCountStaffingTable(props: PropsTypes) {
  const { rpmData, datePeriod } = props;
  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
         Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() => downloadExcelStaffingPropertyCount(rpmData)}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <CommonTable>
        <thead>
          <CommonTableSubHeaderRow>
            <th className="text-left">RPM Name</th>
            <CommonTableHeadingCell borderLeft>
              Stabilized
            </CommonTableHeadingCell>
            <CommonTableHeadingCell>Lease-Up</CommonTableHeadingCell>
            <CommonTableHeadingCell>New Construction</CommonTableHeadingCell>
            <CommonTableHeadingCell>Total Properties</CommonTableHeadingCell>
            <CommonTableHeadingCell borderRight>
              Expected PM Fees
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>
        <tbody>
          {rpmData?.map((item) => {
            return (
              <CommonTableBodyRow key={item?.rpm_name}>
                <td className="text-left">{item?.rpm_name}</td>

                <CommonTableBodyCell borderLeft>
                  {item?.stabilized}
                </CommonTableBodyCell>
                <CommonTableBodyCell>{item?.lease_up}</CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.new_construction}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.total_properties}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {item?.expected_pm_fees}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
        </tbody>
      </CommonTable>
    </div>
  );
}

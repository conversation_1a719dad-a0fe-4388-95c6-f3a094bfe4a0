import { BusinessDevelopmentItem } from '@/slice/businessDevelopmentSlice';
import ExcelJS from 'exceljs';
import { toast } from 'sonner';

export const exportBusinessDevelopmentToExcel = async (
  data: BusinessDevelopmentItem[],
  lastUpdated: string,
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Business Development KPIs');

    // Add title
    const titleRow = worksheet.addRow(['Business Development KPIs']);
    titleRow.font = { size: 16, bold: true };
    worksheet.mergeCells('A1:I1');

    // Add subtitle
    const subtitleRow = worksheet.addRow([`Current Month Wins/Losses - ${lastUpdated}`]);
    subtitleRow.font = { size: 12, italic: true };
    worksheet.mergeCells('A2:I2');

    // Add empty row
    worksheet.addRow([]);

    // Add headers
    const headerRow = worksheet.addRow([
      'Property',
      'Type',
      'Client',
      'Units',
      'Status',
      'Prior/New Mgr.',
      'PM Fee %',
      '25 PM Fees',
      'Annual PM Fees',
    ]);

    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '43298F' },
    };
    headerRow.font = { color: { argb: 'FFFFFF' }, bold: true };

    // Property names
    const propertyNames = ['The Palms', 'Rockcrest', 'Villas at Beach', 'Oceanview'];

    // Add data rows
    data.forEach((item, index) => {
      const row = worksheet.addRow([
        propertyNames[index] || '',
        item.type,
        item.client,
        item.units,
        item.status,
        item.manager,
        `${item.pmFeePercent.toFixed(1)}%`,
        item.pmFeePer25,
        item.annualPmFees,
      ]);

      // Style Win/Loss differently
      if (item.type === 'Win') {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E8F5E8' },
        };
      } else if (item.type === 'Loss') {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE8E8' },
        };
      }
    });

    // Add total row
    const totalUnits = data.reduce((sum, item) => sum + item.units, 0);
    const totalAnnualPmFees = data.reduce((sum, item) => sum + item.annualPmFees, 0);

    const totalRow = worksheet.addRow([
      'Total',
      '',
      '',
      totalUnits,
      '',
      '',
      '',
      '',
      totalAnnualPmFees,
    ]);

    totalRow.font = { bold: true };
    totalRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'DEEFFF' },
    };

    // Format columns
    worksheet.getColumn(4).numFmt = '#,##0'; // Units
    worksheet.getColumn(8).numFmt = '$#,##0'; // 25 PM Fees
    worksheet.getColumn(9).numFmt = '$#,##0'; // Annual PM Fees

    // Set column widths
    worksheet.columns.forEach((column, index) => {
      const lengths = column.values?.map((v) => v?.toString().length) || [];
      const maxLength = Math.max(...lengths.filter((v) => typeof v === 'number'));
      column.width = Math.min(Math.max(maxLength + 2, 10), 50);
    });

    // Add borders
    const borderStyle = {
      top: { style: 'thin' as const },
      left: { style: 'thin' as const },
      bottom: { style: 'thin' as const },
      right: { style: 'thin' as const },
    };

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber >= 4) {
        // Skip title rows
        row.eachCell((cell) => {
          cell.border = borderStyle;
        });
      }
    });

    // Generate and download file
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Business_Development_KPIs_${new Date().toISOString().slice(0, 10)}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);

    toast.success('Excel file exported successfully');
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('Failed to export Excel file');
  }
};
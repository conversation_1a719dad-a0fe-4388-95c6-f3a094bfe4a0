import React from 'react';
import { RootState } from '@/store';
import { FileUp } from 'lucide-react';
import { useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';

const PropertyPerformanceTable: React.FC = () => {
  const { propertyPerformanceData } = useSelector((state: RootState) => state.propertyManagementKpi);

  const handleExport = () => {
    console.log('Export Property Performance data');
  };

  return (
    <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg p-4">
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Property-Level Avg. Performance vs. Budget (All Properties)
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={handleExport}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <div className="border border-[#F3F4FF] overflow-hidden">
        <table className="min-w-full border-collapse">
          <thead>
            <tr className="bg-[#43298F] text-white">
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                P&L Line Item
              </th>
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                Central
              </th>
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                East
              </th>
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                West
              </th>
              <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                Consolidated
              </th>
            </tr>
          </thead>
          <tbody className="bg-white">
            {propertyPerformanceData.map((item, index) => (
              <tr
                key={index}
                className={`border-b border-[#DEEFFF] ${
                  index % 2 === 1 ? 'bg-[#F4F4FF]' : 'bg-white'
                }`}
              >
                <td className="px-2 py-0 whitespace-nowrap text-left font-medium border-r border-[#DEEFFF]">
                  {item.lineItem}
                </td>
                <td className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]">
                  {item.central}
                </td>
                <td className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]">
                  {item.east}
                </td>
                <td className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]">
                  {item.west}
                </td>
                <td className="px-2 py-0 whitespace-nowrap text-center">
                  {item.consolidated}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PropertyPerformanceTable;
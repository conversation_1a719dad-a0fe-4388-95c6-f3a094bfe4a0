import React from 'react';
import { setActiveTab } from '@/slice/propertyManagementKpiSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Container } from '@/components/common/container';
import ReportFilters from '../IncomeReport/components/ReportFilters';
import OccupancyRentsTable from './components/OccupancyRentsTable';
import OperationalKPIsTable from './components/OperationalKPIsTable';
import PropertyPerformanceTable from './components/PropertyPerformanceTable';

const PropertyManagementKPIsPage: React.FC = () => {
  const dispatch = useDispatch();
  const { activeTab, loading, error } = useSelector(
    (state: RootState) => state.propertyManagementKpi,
  );

  const handleTabChange = (value: string) => {
    dispatch(setActiveTab(value as 'occupancyRents' | 'operationalKpis' | 'propertyPerformance'));
  };

  const TabsContentUi = ({
    loading,
    error,
    component,
  }: {
    loading?: boolean;
    error?: string | null;
    component: React.ReactNode;
  }) => {
    return (
      <div>
        {loading ? (
          <div className="h-[60vh] flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#43298F]"></div>
          </div>
        ) : error ? (
          <div className="text-red-500 text-center py-8">{error}</div>
        ) : (
          component
        )}
      </div>
    );
  };

  return (
    <Container title="Property Management KPIs" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>

        <div className="min-w-[1024px]">
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid w-full grid-cols-3 mb-6 bg-white border border-[#43298F]">
              <TabsTrigger
                value="occupancyRents"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
              >
                Occupancy & Rents
              </TabsTrigger>
              <TabsTrigger
                value="operationalKpis"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
              >
                Operational KPIs
              </TabsTrigger>
              <TabsTrigger
                value="propertyPerformance"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
              >
                Property Performance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="occupancyRents">
              <TabsContentUi
                loading={loading}
                error={error}
                component={<OccupancyRentsTable />}
              />
            </TabsContent>

            <TabsContent value="operationalKpis">
              <TabsContentUi
                loading={loading}
                error={error}
                component={<OperationalKPIsTable />}
              />
            </TabsContent>

            <TabsContent value="propertyPerformance">
              <TabsContentUi
                loading={loading}
                error={error}
                component={<PropertyPerformanceTable />}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Container>
  );
};

export default PropertyManagementKPIsPage;
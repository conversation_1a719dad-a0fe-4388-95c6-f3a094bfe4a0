import React from 'react';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';

// Tabs List
type ReportsTabsListProps = {
  className: string;
  children: React.ReactNode;
};

export const ReportsTabsList: React.FC<ReportsTabsListProps> = ({
  className,
  children,
}) => {
  return (
    <TabsList
      className={` w-full  mb-6 bg-white border border-[#43298F] 
        grid ${className}
        `}
    >
      {children}
    </TabsList>
  );
};

// Tabs Trigger
type ReportTabsTriggerProps = {
  value: string;
  tabName: string;
};
export const ReportTabsTrigger: React.FC<ReportTabsTriggerProps> = ({
  value,
  tabName,
}) => {
  return (
    <TabsTrigger
      value={value}
      className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
    >
      {tabName}
    </TabsTrigger>
  );
};

export const ReportTabsContentUILayout = ({
  loading,
  error,
  component,
}: {
  loading?: boolean;
  error?: string | null;
  component: React.ReactNode;
}) => {
  return (
    <div>
      {loading ? (
        <div className="h-[60vh] flex flex-col items-center justify-center bg-[#F4F4FF] rounded-lg p-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#43298F]"></div>
          <div>Loading...</div>
        </div>
      ) : error ? (
        <div className="text-red-500 text-center py-8">{error}</div>
      ) : (
        <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg p-4">
          {component}
        </div>
      )}
    </div>
  );
};

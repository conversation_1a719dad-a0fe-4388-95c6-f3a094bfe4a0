.table1 {
  /* @apply min-w-full border-collapse; */
  /* @apply border-collapse; */
  /* table-layout: fixed;
  width: 100%; */
}

.bodyCell {
  border-left: 1px solid #deefff;
  font-weight: 400;
  font-size: 15px;
}

.headingMergeCell {
  border: 2px solid black;
  border-top-width: 1px;
}

.subHeadingCell {
  background-color: #43298f;
  color: #fff;
  border-right: 1px solid black;
}

.borderLeft {
  border-left: 2px solid black;
}

.borderRight {
  border-right: 2px solid black;
}

.bodyRow:nth-child(even) {
  /* background: #f4f4ff; */
  border: 1px solid #deefff;
}
.bodyRow:nth-child(odd) {
  background: #fff;
  /* border: 1px solid #deefff; */
}

.lastRowBorderBtm:last-child td:not(:first-child),
.lastRowBorderBtm:last-child th:not(:first-child) {
  border-bottom: 2px solid #000;
}

.lastRowFirstCellBorderBtm:last-child th:nth-child(1),
.lastRowFirstCellBorderBtm:last-child td:nth-child(1) {
  border-bottom: 2px solid #000;
}

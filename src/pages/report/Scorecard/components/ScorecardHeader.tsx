import React from 'react';
import { setFilters } from '@/slice/scoreCardSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { RESPONSIVE_FONTS, FONT_SIZES } from '../constants/typography';

const ScorecardHeader = () => {
  const dispatch = useDispatch();
  const { title, filters } = useSelector((state: RootState) => state.scorecard);

  return (
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className="bg-purple-600 p-2 rounded">
              <span className={`text-white font-bold ${FONT_SIZES.sm}`}>WB</span>
            </div>
            <div>
              <h1 className={`${RESPONSIVE_FONTS.pageTitle} font-semibold text-gray-900`}>{title}</h1>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className={`${RESPONSIVE_FONTS.filterLabel} font-medium text-gray-700`}>System:</label>
            <select
              value={filters.system}
              onChange={(e) => dispatch(setFilters({ system: e.target.value }))}
              className={`px-3 py-1 border border-gray-300 rounded ${RESPONSIVE_FONTS.filterLabel}`}
            >
              <option value="Yardi 7s/RentCafe - WILL">
                Yardi 7s/RentCafe - WILL
              </option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <label className={`${RESPONSIVE_FONTS.filterLabel} font-medium text-gray-700`}>
              Property:
            </label>
            <select
              value={filters.property}
              onChange={(e) =>
                dispatch(setFilters({ property: e.target.value }))
              }
              className={`px-3 py-1 border border-gray-300 rounded ${RESPONSIVE_FONTS.filterLabel}`}
            >
              <option value="22506 - The Earl">22506 - The Earl</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <label className={`${RESPONSIVE_FONTS.filterLabel} font-medium text-gray-700`}>
              Reporting Period:
            </label>
            <input
              type="text"
              value={filters.reportingPeriod}
              onChange={(e) =>
                dispatch(setFilters({ reportingPeriod: e.target.value }))
              }
              className={`px-3 py-1 border border-gray-300 rounded ${RESPONSIVE_FONTS.filterLabel}`}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScorecardHeader;

/**
 * Central typography configuration for Scorecard components
 * Reduced font sizes to ensure all content fits on one page without scrolling
 */

// Base font sizes (reduced from standard Tailwind classes)
export const FONT_SIZES = {
  // Tiny text (10px) - for very small labels
  tiny: 'text-[10px]',
  
  // Extra small (12px) - for labels, minor text
  xs: 'text-xs',
  
  // Small (14px) - for regular content, most text
  sm: 'text-sm',
  
  // Base (16px) - for emphasized content
  base: 'text-base',
  
  // Large (18px) - for section headers, important text
  lg: 'text-lg',
} as const;

// Responsive font size patterns
export const RESPONSIVE_FONTS = {
  // Metric values: tiny → xs → sm (responsive scaling)
  metricValue: 'text-[10px] sm:text-xs lg:text-sm',
  
  // Metric labels: tiny → xs (responsive scaling)  
  metricLabel: 'text-[10px] sm:text-xs',
  
  // Section headers: xs → sm (responsive scaling)
  sectionHeader: 'text-xs sm:text-sm',
  
  // Page titles: sm → base (responsive scaling)
  pageTitle: 'text-sm sm:text-base',
  
  // Table content: xs (static)
  tableContent: 'text-xs',
  
  // Filter labels: xs (static)
  filterLabel: 'text-xs',
  
  // Button text: xs (static)
  buttonText: 'text-xs',
  
  // Property details: xs (static)
  propertyDetails: 'text-xs',
  
  // Summary text: xs → sm (responsive scaling)
  summaryText: 'text-xs sm:text-sm',
  
  // Error messages: sm (static)
  errorMessage: 'text-sm',
  
  // Loading skeleton: xs (static)
  loadingSkeleton: 'text-xs',
} as const;

// Legacy font size mappings for easy migration
export const LEGACY_FONT_MAPPING = {
  // Old → New mappings for easy replacement
  'text-xl': RESPONSIVE_FONTS.pageTitle,
  'text-lg': FONT_SIZES.sm,
  'text-base': FONT_SIZES.xs,
  'text-sm': FONT_SIZES.xs,
  'text-xs': FONT_SIZES.tiny,
  
  // Responsive mappings
  'text-lg sm:text-xl': RESPONSIVE_FONTS.pageTitle,
  'text-sm sm:text-base': RESPONSIVE_FONTS.sectionHeader,
  'text-sm sm:text-base lg:text-xl': RESPONSIVE_FONTS.metricValue,
  'text-xs sm:text-sm': RESPONSIVE_FONTS.metricLabel,
} as const;

// CSS font sizes for custom styling (e.g., PDF generation)
export const CSS_FONT_SIZES = {
  tiny: '10px',
  xs: '12px',
  sm: '14px',
  base: '16px',
  lg: '18px',
} as const;

// Icon sizes that complement the reduced font sizes
export const ICON_SIZES = {
  tiny: 'w-3 h-3',
  xs: 'w-4 h-4',
  sm: 'w-5 h-5',
  base: 'w-6 h-6',
  lg: 'w-7 h-7',
} as const;

// Utility type for font size keys
export type FontSizeKey = keyof typeof FONT_SIZES;
export type ResponsiveFontKey = keyof typeof RESPONSIVE_FONTS;
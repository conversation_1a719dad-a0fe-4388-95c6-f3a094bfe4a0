export type MetricColor = 'green' | 'yellow' | 'red' | undefined;

export const getMetricColor = (
  value: number,
  isPositive: boolean,
): MetricColor => {
  if (isPositive && value > 0) return 'green';
  if (!isPositive && value < 0) return 'red';
  if (isPositive && value < 0) return 'red';
  if (!isPositive && value > 0) return 'green';
  return 'yellow';
};

export const getTextColorClass = (color?: MetricColor | string): string => {
  switch (color) {
    case 'green':
      return 'text-green-600';
    case 'yellow':
      return 'text-yellow-500';
    case 'red':
      return 'text-red-500';
    default:
      return 'text-gray-900';
  }
};

// Occupancy & Leasing Metrics Color Logic
export const getOccupancyPercentageColor = (value: number): MetricColor => {
  if (value < 90) return 'red';
  if (value >= 90 && value <= 94.9) return 'yellow';
  if (value >= 95) return 'green';
  return undefined;
};

export const getPerformanceToSubmarketColor = (value: number): MetricColor => {
  return value > 0 ? 'green' : 'red';
};

export const getOccupancyTrendColor = (value: number): MetricColor => {
  if (value < 90) return 'red';
  if (value >= 90 && value <= 92) return 'yellow';
  if (value > 92) return 'green';
  return undefined;
};

export const getVarianceToOccupancyColor = (value: number): MetricColor => {
  const absValue = Math.abs(value);
  if (absValue > 5) return 'red';
  if (absValue > 3 && absValue <= 5) return 'yellow';
  if (absValue <= 3) return 'green';
  return undefined;
};

export const getOccupancyTrendT30ChangeColor = (
  value: number,
  occupancy: number,
  submarketOccupancy: number,
): MetricColor => {
  if (occupancy >= submarketOccupancy) {
    return value >= 0 ? 'green' : 'red';
  } else {
    return value <= 0 ? 'red' : 'green';
  }
};

export const getShowsT30Color = (
  value: number,
  totalUnits?: number,
): MetricColor => {
  if (!totalUnits) return undefined;

  const threshold = (totalUnits * 0.5) / 12 / 0.35;
  return value < threshold ? 'red' : 'green';
};

export const getTrendPercentGainLossColor = (value: number): MetricColor => {
  const absValue = Math.abs(value);
  if (absValue < 1) return 'green';
  if (absValue >= 1 && absValue < 3) return 'yellow';
  if (absValue >= 3) return 'red';
  return undefined;
};

export const getAgedVacantUnitsColor = (
  agedVacantUnits: number,
  totalUnits: number,
): MetricColor => {
  if (totalUnits === 0) return undefined;
  const percentage = (agedVacantUnits / totalUnits) * 100;
  return percentage > 2 ? 'red' : 'green';
};

export const getAvgDaysVacantColor = (value: number): MetricColor => {
  return value > 30 ? 'red' : 'green';
};

// Rental Metrics Color Logic
export const getRentalYoYChangeColor = (value: number): MetricColor => {
  if (value < -5) return 'red';
  if (value >= -5 && value <= 0.9) return 'yellow';
  if (value >= 1 && value <= 5) return 'green';
  if (value > 5) return 'green';
  return undefined;
};

export const getYTDRenewalConversionColor = (value: number): MetricColor => {
  if (value < 40) return 'red';
  if (value >= 40 && value < 50) return 'yellow';
  if (value >= 50 && value <= 60) return 'green';
  if (value > 60) return 'green';
  return undefined;
};

// Financial Metrics Color Logic
export const getIncomePerformanceToBudgetColor = (
  value: number,
): MetricColor => {
  if (value > 2) return 'green';
  if (value > -1 && value <= 2) return 'yellow';
  if (value <= -2) return 'red';
  if (value === -1) return 'yellow';
  return undefined;
};

export const getControllableOpexColor = (value: number): MetricColor => {
  if (value <= -3) return 'green';
  if (value > -3 && value <= 0) return 'yellow';
  if (value > 0 && value < 3) return 'yellow';
  if (value >= 3) return 'red';
  return undefined;
};

export const getTotalOpexColor = (value: number): MetricColor => {
  if (value <= -3) return 'green';
  if (value > -3 && value <= 0) return 'yellow';
  if (value > 0 && value < 3) return 'yellow';
  if (value >= 3) return 'red';
  return undefined;
};

export const getNOIColor = (value: number): MetricColor => {
  if (value >= 2) return 'green';
  if (value < 0) return 'yellow';
  if (value >= 0 && value < 2) return 'yellow';
  if (value <= -2) return 'red';
  return undefined;
};

export const getControllableNOIColor = (value: number): MetricColor => {
  if (value >= 2) return 'green';
  if (value < 0) return 'yellow';
  if (value >= 0 && value < 2) return 'yellow';
  if (value <= -2) return 'red';
  return undefined;
};

export const getCapitalColor = (value: number): MetricColor => {
  if (value >= 2) return 'green';
  if (value < 0) return 'yellow';
  if (value >= 0 && value < 2) return 'yellow';
  if (value <= -2) return 'red';
  return undefined;
};

// Collections & Bad Debt Color Logic
export const getCollectionsMTDColor = (value: number): MetricColor => {
  if (value >= 98) return 'green';
  if (value >= 85 && value <= 97.9) return 'yellow';
  if (value < 85) return 'red';
  return undefined;
};

export const getBadDebtWOColor = (value: number): MetricColor => {
  if (value >= -1) return 'green';
  if (value >= -2 && value <= -1.1) return 'yellow';
  if (value < -2) return 'red';
  return undefined;
};

export const getCollectionRecoveryRatioColor = (
  value: number,
  badDebtYTD: number = 0,
): MetricColor => {
  // Special case: if $0 Bad Debt YTD = Green
  if (badDebtYTD === 0) return 'green';

  if (value >= 15) return 'green';
  if (value >= 10 && value <= 14.9) return 'yellow';
  if (value < 10) return 'red';
  return undefined;
};

// Facilities Maintenance & Capital Color Logic
export const getAvgUnitTurnTimeColor = (value: number): MetricColor => {
  if (value <= 10) return 'green';
  if (value >= 11 && value <= 12) return 'yellow';
  if (value >= 13) return 'red';
  return undefined;
};

export const getRepeatServiceTicketsColor = (value: number): MetricColor => {
  if (value < 1) return 'green';
  if (value >= 2 && value <= 5) return 'yellow';
  if (value > 5) return 'red';
  return undefined;
};

export const getTicketsOver72HrsColor = (value: number): MetricColor => {
  if (value <= 5) return 'green';
  if (value > 5 && value <= 10) return 'yellow';
  if (value > 10) return 'red';
  return undefined;
};

export const getCapitalExecutionColor = (value: number): MetricColor => {
  if (value >= 85 && value <= 115) return 'green';
  if ((value >= 70 && value < 85) || (value > 115 && value <= 130))
    return 'yellow';
  if (value < 70 || value > 130) return 'red';
  return undefined;
};

// Legacy functions for backward compatibility
export const getOccupancyColor = (value: number): MetricColor => {
  return getOccupancyPercentageColor(value);
};

export const getRentalYoYColor = (value: number): MetricColor => {
  return getRentalYoYChangeColor(value);
};

export const getCollectionColor = (value: number): MetricColor => {
  return getCollectionsMTDColor(value);
};

export const getPerformanceScoreIcon = (
  category: string,
  actualValue: string,
): string => {
  const isPercentage = actualValue.includes('%');
  const numValue = parseFloat(actualValue.replace(/[%$,]/g, ''));

  if (isNaN(numValue)) return '';

  const categoryLower = category.toLowerCase();

  if (categoryLower.includes('collection') && categoryLower.includes('mtd')) {
    // Value is already a percentage, don't multiply
    const color = getCollectionsMTDColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (categoryLower.includes('bad debt')) {
    const color = getBadDebtWOColor(numValue);
    return getScoreIcon(color);
  }

  if (
    categoryLower.includes('collection') &&
    categoryLower.includes('recovery')
  ) {
    // Value is already a percentage, don't multiply
    const color = getCollectionRecoveryRatioColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (
    categoryLower.includes('unit turn') ||
    categoryLower.includes('turn time')
  ) {
    const color = getAvgUnitTurnTimeColor(numValue);
    return getScoreIcon(color);
  }

  if (categoryLower.includes('repeat service ticket')) {
    // Value is already a percentage, don't multiply
    const color = getRepeatServiceTicketsColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (categoryLower.includes('tickets') && categoryLower.includes('72')) {
    // Value is already a percentage, don't multiply
    const color = getTicketsOver72HrsColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (categoryLower.includes('capital execution')) {
    // Value is already a percentage, don't multiply
    const color = getCapitalExecutionColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  return '';
};

export const getScoreIcon = (score: MetricColor | string): string => {
  switch (score) {
    case 'green':
      return '🟢';
    case 'yellow':
      return '⚠️';
    case 'red':
      return '❌';
    default:
      return '';
  }
};

export const getMetricBackgroundColor = (label: string): string | undefined => {
  if (label === 'OCCUPANCY TREND T30 VARIANCE' || label === 'SHOWS T30') {
    return '#F2F2F2';
  }
  return undefined;
};

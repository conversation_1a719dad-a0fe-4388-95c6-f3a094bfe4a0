import html2canvas from 'html2canvas-pro';

export interface ScreenshotOptions {
  scale: number;
  backgroundColor: string;
  useCORS: boolean;
  allowTaint: boolean;
  width?: number;
  height?: number;
  scrollX?: number;
  scrollY?: number;
}

export interface ScreenshotResult {
  success: boolean;
  dataUrl?: string;
  error?: string;
}

const DEFAULT_OPTIONS: ScreenshotOptions = {
  scale: 2,
  backgroundColor: '#f8fafc',
  useCORS: true,
  allowTaint: false,
  scrollX: 0,
  scrollY: 0,
};

const isProduction = import.meta.env.PROD;
const IMAGE_LOAD_TIMEOUT = isProduction ? 10000 : 5000;

const shouldUseBase64ForImage = (src: string): boolean => {
  if (src.includes('maps.googleapis.com')) return true;

  if (src.includes('api.') && src.startsWith('http')) return true;

  return false;
};

const validateBase64Image = (base64String: string): boolean => {
  try {
    if (!base64String.startsWith('data:image/')) return false;
    const base64Data = base64String.split(',')[1];
    if (!base64Data || base64Data.length < 100) return false;

    atob(base64Data);
    return true;
  } catch {
    return false;
  }
};

const convertImageToBase64 = (url: string): Promise<string | null> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    const timeout = setTimeout(() => {
      console.warn(`Image conversion timeout for: ${url}`);
      resolve(null);
    }, 5000);

    img.onload = () => {
      clearTimeout(timeout);
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          console.error('Could not get canvas context');
          resolve(null);
          return;
        }
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        const base64 = canvas.toDataURL('image/png');
        canvas.remove();
        resolve(base64);
      } catch (error) {
        console.error(`Error converting image to base64: ${error}`);
        resolve(null);
      }
    };

    img.onerror = (error) => {
      clearTimeout(timeout);
      console.error(`Image load error for: ${url}`, error);
      resolve(null);
    };

    img.src = url;
  });
};

const preprocessImages = async (element: HTMLElement): Promise<void> => {
  const images = element.querySelectorAll('img');

  const imageProcessingPromises = Array.from(images).map(async (img) => {
    const src = img.src;

    if (!src || src === '') {
      console.warn('Empty image src detected, replacing with placeholder');
      img.src = '/api/placeholder/400/200';
      return;
    }

    if (src.startsWith('data:image/')) {
      if (!validateBase64Image(src)) {
        console.warn(
          'Invalid base64 image detected, replacing with placeholder',
        );
        img.src = '/api/placeholder/400/200';
      } else {
      }
      return;
    }

    if (src.startsWith('http')) {
      if (shouldUseBase64ForImage(src)) {
        img.src = '/api/placeholder/400/200';
        return;
      }

      try {
        const base64 = await convertImageToBase64(src);
        if (base64) {
          img.src = base64;
        } else {
          img.src = '/api/placeholder/400/200';
        }
      } catch (error) {
        console.error(`Error converting image to base64: ${error}`);
        img.src = '/api/placeholder/400/200';
      }
    }

    if (src.startsWith('/')) {
    }
  });

  await Promise.all(imageProcessingPromises);
};

const waitForContentToLoad = async (
  element: HTMLElement,
  timeout: number = IMAGE_LOAD_TIMEOUT,
): Promise<void> => {
  return new Promise(async (resolve) => {
    const startTime = Date.now();

    try {
      await preprocessImages(element);
    } catch (error) {
      console.warn('Error preprocessing images:', error);
    }

    const checkContent = () => {
      const images = element.querySelectorAll('img');
      const imagePromises = Array.from(images).map((img) => {
        if (img.complete && img.naturalWidth > 0) return Promise.resolve();
        return new Promise((resolve) => {
          const timeout = setTimeout(() => {
            console.warn('Image load timeout for:', img.src);
            resolve(void 0);
          }, 3000);

          img.onload = () => {
            clearTimeout(timeout);
            resolve(void 0);
          };
          img.onerror = () => {
            clearTimeout(timeout);
            console.warn('Image failed to load:', img.src);

            img.src = '/api/placeholder/400/200';
            resolve(void 0);
          };
        });
      });

      const chartElements = element.querySelectorAll(
        '[data-apexcharts-rendered]',
      );
      const chartsReady =
        chartElements.length === 0 ||
        Array.from(chartElements).every((chart) => chart.querySelector('svg'));

      const antChartElements = element.querySelectorAll('.ant-chart');
      const antChartsReady =
        antChartElements.length === 0 ||
        Array.from(antChartElements).every((chart) =>
          chart.querySelector('canvas, svg'),
        );

      Promise.all(imagePromises).then(() => {
        if (chartsReady && antChartsReady) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          console.warn(
            'Screenshot capture timeout - proceeding with current state',
          );
          resolve();
        } else {
          setTimeout(checkContent, 100);
        }
      });
    };

    checkContent();
  });
};

const attemptScreenshotWithOptions = async (
  element: HTMLElement,
  options: ScreenshotOptions,
): Promise<HTMLCanvasElement> => {
  return html2canvas(element, {
    scale: options.scale,
    backgroundColor: options.backgroundColor,
    useCORS: options.useCORS,
    allowTaint: options.allowTaint,
    width: options.width,
    height: options.height,
    scrollX: options.scrollX,
    scrollY: options.scrollY,
    onclone: (clonedDoc) => {
      const clonedElement =
        clonedDoc.querySelector('[data-screenshot-target]') || clonedDoc.body;
      if (clonedElement) {
        (clonedElement as HTMLElement).style.fontFamily = 'inherit';
      }
    },
  });
};

export const captureElementAsImage = async (
  element: HTMLElement,
  options: Partial<ScreenshotOptions> = {},
): Promise<ScreenshotResult> => {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };

  try {
    await waitForContentToLoad(element);
    await new Promise((resolve) => setTimeout(resolve, 500));

    const corsStrategies = [
      { useCORS: true, allowTaint: false, description: 'Strict CORS' },
      { useCORS: true, allowTaint: true, description: 'CORS with taint' },
      { useCORS: false, allowTaint: true, description: 'No CORS with taint' },
    ];

    let lastError: Error | null = null;

    for (let i = 0; i < corsStrategies.length; i++) {
      const strategy = corsStrategies[i];
      const attemptOptions = { ...mergedOptions, ...strategy };

      try {
        const canvas = await attemptScreenshotWithOptions(
          element,
          attemptOptions,
        );
        const dataUrl = canvas.toDataURL('image/png', 0.95);
        canvas.remove();

        return {
          success: true,
          dataUrl,
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        console.warn(
          `Screenshot failed with strategy ${strategy.description}:`,
          error,
        );

        if (i < corsStrategies.length - 1) {
          continue;
        }
      }
    }

    throw lastError || new Error('All screenshot strategies failed');
  } catch (error) {
    console.error('Screenshot capture failed with all strategies:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

export const validateScreenshot = (dataUrl: string): boolean => {
  if (!dataUrl || !dataUrl.startsWith('data:image/')) {
    return false;
  }

  const base64Data = dataUrl.split(',')[1];
  if (!base64Data || base64Data.length < 1000) {
    return false;
  }

  return true;
};

export const debugImageLoadingIssues = (element: HTMLElement): void => {
  const images = element.querySelectorAll('img');

  images.forEach((img, index) => {
    const imageInfo = {
      index: index + 1,
      src: img.src,
      alt: img.alt,
      complete: img.complete,
      naturalWidth: img.naturalWidth,
      naturalHeight: img.naturalHeight,
      crossOrigin: img.crossOrigin,
      loading: img.loading,
      hasLoadedAttribute: img.hasAttribute('data-image-loaded'),
      hasErrorAttribute: img.hasAttribute('data-image-error'),
      isBase64: img.src.startsWith('data:image/'),
      isExternal: img.src.startsWith('http'),
      isLocal: img.src.startsWith('/'),
      isGoogleMaps: img.src.includes('maps.googleapis.com'),
    };

    if (!img.complete || img.naturalWidth === 0) {
      console.warn(`Image ${index + 1} may not be fully loaded`);
    }

    if (img.hasAttribute('data-image-error')) {
      console.error(`Image ${index + 1} had loading errors`);
    }

    if (imageInfo.isGoogleMaps) {
      console.error(
        `Google Maps URL detected - this will cause CORS issues in production!`,
      );
      console.error(`URL: ${img.src.substring(0, 100)}...`);
    }

    if (imageInfo.isBase64) {
      //  const base64Size = Math.round((img.src.length * 3) / 4 / 1024);
    }
  });
};

export const compressImage = (
  dataUrl: string,
  maxSizeKB: number = 5000,
): Promise<string> => {
  const base64Data = dataUrl.split(',')[1];
  const sizeKB = (base64Data.length * 3) / 4 / 1024;

  if (sizeKB <= maxSizeKB) {
    return Promise.resolve(dataUrl);
  }

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const img = new Image();

  return new Promise<string>((resolve) => {
    img.onload = () => {
      const ratio = Math.sqrt(maxSizeKB / sizeKB);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;

      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);

      let quality = 0.9;
      let compressedDataUrl = canvas.toDataURL('image/jpeg', quality);

      while (
        compressedDataUrl.length > (maxSizeKB * 1024 * 4) / 3 &&
        quality > 0.3
      ) {
        quality -= 0.1;
        compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
      }

      canvas.remove();
      resolve(compressedDataUrl);
    };

    img.src = dataUrl;
  }).catch(() => dataUrl);
};

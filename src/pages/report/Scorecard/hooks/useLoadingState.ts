import { useSelector } from 'react-redux';
import { RootState } from '@/store';

/**
 * Custom hook to get loading state for a specific scorecard section
 * @param section - The section to get loading state for
 * @returns The loading state boolean
 */
export const useLoadingState = (
  section: keyof RootState['scorecard']['loadingStates']
): boolean => {
  const loadingStates = useSelector((state: RootState) => state.scorecard.loadingStates);
  return loadingStates[section];
};

/**
 * Custom hook to get all scorecard data and loading states
 * @returns Object containing all scorecard data and loading states
 */
export const useScorecardState = () => {
  const scorecard = useSelector((state: RootState) => state.scorecard);
  return scorecard;
};